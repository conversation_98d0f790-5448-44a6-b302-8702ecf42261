<template>
  <view
    class="w-full max-w-[750rpx] min-h-screen flex flex-col items-center justify-between mx-auto pt-0 ios-safe-area">
    <!-- 上半部分：图片展示 -->

    <view style="transition: all 0.3s;justify-content: center;" class="w-full flex-1 flex flex-col relative topImg">
      <view v-if="!generate.length" class="image-container" style="position: relative; display: inline-block;">
        <image class="beforeimg" ref="beforeimgRef" :src="props.peopleImg" mode="widthFix" style="width: 100%;"
          @load="onImageLoad" />
        <canvas v-if="showCanvas" canvas-id="eyeCanvas" id="eyeCanvas" :style="canvasStyle"
          style="position: absolute; top: 0; left: 0; pointer-events: none;"></canvas>
        <!-- 光点动画容器 -->
        <view v-for="(animation, index) in loadingAnimations" :key="animation.id || index" class="light-point" :style="{
          left: animation.x + 'px',
          top: animation.y + 'px',
          opacity: animation.visible ? 1 : 0
        }"></view>
      </view>
      <ImgCompare v-else :before="props.peopleImg" :after="generate" :height="1000" />
    </view>
    <view v-if="!!props.activeReport" class="popup-container"
      :class="[popupVisible ? 'slide-up' : 'slide-down', isExpanded ? 'expanded' : 'collapsed']" @click.stop
      @touchstart.passive @touchmove.passive>
      <!-- <view class="popup-header">
          <text class="popup-title"></text>
          <uni-icons type="closeempty" size="24" color="#666" @click="closePopup"></uni-icons>
        </view> -->

      <view v-if="props.activeReport == '诊断报告'" class=" ">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 " v-if="'defect_report' in face_aes">
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">下巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.chin.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">轮廓</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.contour.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">眼睛</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.eyes.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">额头</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.forehead.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">嘴巴</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.mouth.description }}
          </text>
          <text class="text-base mb-1 block" style="font-size: 28rpx;font-weight: 500;">鼻子</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.defect_report.nose.description }}
          </text>
          <view>
            您的总体评分：<view class="ml-4" style="display: inline-flex;">
              {{ face_aes.overall_score }}
            </view>
          </view>
        </view>
      </view>
      <template v-if="props.activeReport == '美学方案'">
        <button @click="handlePreview()" style="margin: 0 auto;padding: 0;line-height: 30rpx;"
          class="bg-[#FF8F9C] text-white text-xs rounded  w-10 py-1 font-medium flex justify-center ios-button">
          <up-icon v-if="!isExpanded" size="20" color="#fff" name="arrow-up"></up-icon>
          <up-icon v-else size="20" color="#fff" name="arrow-down"></up-icon>
        </button>
        <view class="popup-content py-1 px-6 m-2 ">
          <template v-for="item in face_aes.treatment_plans.plan_a.projects" :key="item.name">
            <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">{{ item.name }}</text>
            <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
              {{ item.reason }}
            </text>
          </template>
          <text class="text-sm mb-1 block" style="font-size: 28rpx;font-weight: 500;">总结：</text>
          <text class="text-gray-700 mb-2 block" style="font-size: 26rpx;">
            {{ face_aes.treatment_plans.plan_a.reason }}
          </text>
        </view>
      </template>

      <scroll-view v-if="props.activeReport == '专属推荐'" class="popup-content" scroll-x style="white-space: nowrap;">
        <view class="flex flex-nowrap" style="width: max-content;">
          <view class="flex items-center mx-2 p-2 bg-[#F8F8F8]" v-for="(item, index) in recommendedItems"
            @click="gotoOrgList" :key="index">
            <div class="flex flex-col flex-1">
              <div class="text-[14px] text-[#222] leading-tight" style="font-family: 'PingFang SC', Arial;">{{
                item.title }}</div>
              <div class="text-[12px] text-[#999] mt-1" style="font-family: 'PingFang SC', Arial;">{{
                item.category }}
                <span>{{ item.location }}</span>
              </div>
            </div>

            <img :src="item.imgSrc" :alt="item.alt" class="w-14 h-14 rounded-lg object-cover ml-3" />
          </view>
        </view>
      </scroll-view>

    </view>




  </view>
  <!-- 底部导航栏2 -->
  <view v-show="props.activeReport" style="z-index: 5;"
    class="fixed bottom-0 left-0 w-full bg-[#fff] border-t border-[#f0f0f0] transition-transform duration-300 transform ios-bottom-safe"
    :class="{ 'translate-y-0': props.activeReport, 'translate-y-full': !props.activeReport }">
    <view class="flex justify-around items-center h-16 px-4">
      <view v-for="item in reports" style="font-size: 28rpx;border-color: #F39196;" class="font-semi"
        :class="props.activeReport == item ? 'font-semibold border-b-2' : ''" @click="selectIcon2(item)">{{ item
        }}</view>
    </view>
  </view>

</template>

<script setup>
import { useStore } from "vuex"
import ImgCompare from '@/components/imgCompare.vue';
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { callAiPolling } from "@/Api/index.js"
const props = defineProps({
  compareFlag: {
    type: Boolean,
    default: false
  },
  peopleImg: {
    type: String,
    default: ''
  },

  buttonIndex: {
    type: Number,
    default: -1
  },
  activeReport: {
    type: String,
    default: ''
  },
  icons: {
    type: Array,
    default: []
  },
  reports: {
    type: Array,
    default: []
  },

});
let timer = null

// 图片加载完成事件处理
const onImageLoad = () => {
  // 图片加载完成后处理detectInfo
  let detectInfo = uni.getStorageSync('detectInfo')
  if (detectInfo && detectInfo.detect) {
    detectInfo.detect = JSON.parse(detectInfo.detect).faces[0]
    console.log(detectInfo.detect);
    
    // 计算图片宽度比值和处理landmarks
    processDetectInfo(detectInfo)
  }
}

onMounted(() => {

  // emit('update:loading', true)
  // iOS兼容性：初始化状态
  isExpanded.value = false
  isAnimating.value = false
  popupVisible.value = false

  let label = props.icons[0].reports[0];
  selectIcon2(label);
  let operationId = uni.getStorageSync('operationId')
  if (operationId) {
    getStatus(operationId)

    timer = setInterval(async () => {
      getStatus(operationId)
    }, 3000)
  }
})
onBeforeUnmount(() => {

  clearInterval(timer)
})
const emit = defineEmits(['update:activeReport', 'update:loading', 'update:percent', 'show-login']);

const beforeImgHeight = ref(0) // 原图高度
const beforeimgRef = ref(null) // 图片引用
const isExpanded = ref(false) // 控制弹窗展开状态
const isAnimating = ref(false) // 控制动画状态，防止iOS端快速点击

// 处理detectInfo数据
const processDetectInfo = (detectInfo) => {
  // 使用nextTick确保DOM已渲染，然后获取图片实际尺寸
  nextTick(() => {
    setTimeout(() => {
      // 获取图片元素的实际显示尺寸
      const query = uni.createSelectorQuery()
      query.select('.beforeimg').boundingClientRect((rect) => {

        if (rect) {
          const displayWidth = rect.width
          const displayHeight = rect.height

          // 计算宽高比值
          const widthRatio = displayWidth / detectInfo.imgWidth
          const heightRatio = displayHeight / detectInfo.imgHeight

          // 处理landmarks数据，按part分组，并对坐标进行比值计算
          const landmarksObj = {}
          if (detectInfo.detect && detectInfo.detect.landmarks) {
            detectInfo.detect.landmarks.forEach(landmark => {
              const part = landmark.part
              if (!landmarksObj[part]) {
                landmarksObj[part] = []
              }
              landmarksObj[part].push({
                x: landmark.x * widthRatio,  // 对x坐标进行宽度比值计算
                y: landmark.y * heightRatio, // 对y坐标进行高度比值计算
                originalX: landmark.x,       // 保留原始x坐标
                originalY: landmark.y,       // 保留原始y坐标
                id: landmark.id
              })
            })
          }


          // 将数据存储到响应式变量中供后续使用
          imageWidthRatio.value = widthRatio
          imageHeightRatio.value = heightRatio
          landmarksData.value = landmarksObj

          // 初始化canvas绘制
          initCanvasDrawing(displayWidth, displayHeight, landmarksObj)
        } else {
          // console.error('无法获取图片元素尺寸')
        }
      }).exec()
    }, 1000);
  })
}

// 响应式变量存储处理后的数据
const imageWidthRatio = ref(0)
const imageHeightRatio = ref(0)
const landmarksData = ref({})

// Canvas相关变量
const showCanvas = ref(false)
const canvasStyle = ref({})
const loadingAnimations = ref([])
const animationFrames = ref([])

// Canvas绘制和动画相关方法

// 计算路径总长度
const calculatePathLength = (points) => {
  let length = 0;
  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x;
    const dy = points[i + 1].y - points[i].y;
    length += Math.sqrt(dx * dx + dy * dy);
  }
  // 闭合路径
  const dx = points[0].x - points[points.length - 1].x;
  const dy = points[0].y - points[points.length - 1].y;
  length += Math.sqrt(dx * dx + dy * dy);
  return length;
}

// 根据路径长度比例获取对应点
const getPointAtLength = (points, pathLength, targetLength) => {
  let accumulatedLength = 0;

  // 检查闭合路径的最后一段
  const lastSegmentLength = Math.sqrt(
    Math.pow(points[0].x - points[points.length - 1].x, 2) +
    Math.pow(points[0].y - points[points.length - 1].y, 2)
  );

  // 先检查是否在最后一段
  if (targetLength >= pathLength - lastSegmentLength) {
    const progress = (targetLength - (pathLength - lastSegmentLength)) / lastSegmentLength;
    return {
      x: points[points.length - 1].x + (points[0].x - points[points.length - 1].x) * progress,
      y: points[points.length - 1].y + (points[0].y - points[points.length - 1].y) * progress
    };
  }

  // 检查其他段
  for (let i = 0; i < points.length - 1; i++) {
    const segmentLength = Math.sqrt(
      Math.pow(points[i + 1].x - points[i].x, 2) +
      Math.pow(points[i + 1].y - points[i].y, 2)
    );

    if (targetLength <= accumulatedLength + segmentLength) {
      const progress = (targetLength - accumulatedLength) / segmentLength;
      return {
        x: points[i].x + (points[i + 1].x - points[i].x) * progress,
        y: points[i].y + (points[i + 1].y - points[i].y) * progress
      };
    }
    accumulatedLength += segmentLength;
  }

  return points[0]; // 默认返回起点
}

// 创建轮廓光点动画
const createOutlineAnimation = (points, duration = 3000) => {
  const animationId = Date.now() + Math.random();
  const pathLength = calculatePathLength(points);
  let startTime = null;

  // 创建光点元素数据
  const lightPoint = {
    id: animationId,
    x: points[0].x,
    y: points[0].y,
    visible: true
  };

  loadingAnimations.value.push(lightPoint);

  const animate = (timestamp) => {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;

    // 计算归一化的进度(0-1)
    const progress = (elapsed % duration) / duration;

    // 计算当前路径长度位置
    const currentLength = progress * pathLength;

    // 获取对应坐标点
    const position = getPointAtLength(points, pathLength, currentLength);

    // 更新光点位置
    const pointIndex = loadingAnimations.value.findIndex(p => p.id === animationId);
    if (pointIndex !== -1) {
      loadingAnimations.value[pointIndex].x = position.x;
      loadingAnimations.value[pointIndex].y = position.y;
    }

    // 继续动画
    const frameId = requestAnimationFrame(animate);
    animationFrames.value.push(frameId);
  };

  const frameId = requestAnimationFrame(animate);
  animationFrames.value.push(frameId);
}

const createLoadingAnimation = (points) => {
  // 使用新的轮廓动画替代原来的中心点动画
  createOutlineAnimation(points, 2500);
}

const stopAllLoadingAnimations = () => {
  // 停止所有动画帧
  animationFrames.value.forEach(frameId => {
    cancelAnimationFrame(frameId);
  });
  animationFrames.value = [];

  // 隐藏所有光点
  loadingAnimations.value.forEach(animation => {
    animation.visible = false;
  });

  // 0.5秒后清空数组
  setTimeout(() => {
    loadingAnimations.value = [];
  }, 500);
}

const drawEyeOutline = (ctx, points, color = '#00ffaa') => {
  ctx.beginPath();
  ctx.strokeStyle = color;
  ctx.lineWidth = 1;

  // 移动到第一个点
  ctx.moveTo(points[0].x, points[0].y);

  // 连接所有点
  for (let i = 1; i < points.length; i++) {
    ctx.lineTo(points[i].x, points[i].y);
  }

  // 闭合路径
  ctx.closePath();
  ctx.stroke();

  // 绘制点标记
  points.forEach(point => {
    ctx.beginPath();
    ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
  });
}

const initCanvasDrawing = (width, height, landmarks) => {
  // 设置canvas样式
  canvasStyle.value = {
    width: width + 'px',
    height: height + 'px'
  };

  showCanvas.value = true;

  // 等待canvas渲染完成后绘制
  nextTick(() => {
    setTimeout(() => {
      const ctx = uni.createCanvasContext('eyeCanvas');

      // 通用的部位绘制方法
      const drawFacialPart = (partName, color = '#00ffaa', showAnimation = true) => {
        if (landmarks[partName] && landmarks[partName].length > 0) {
          // 绘制轮廓
          drawEyeOutline(ctx, landmarks[partName], color);

          // 如果需要显示动画，创建轮廓光点动画
          if (showAnimation) {
            createLoadingAnimation(landmarks[partName]);
          }

        } else {
          console.log(`${partName} 数据不存在或为空`);
        }
      };

      // 定义需要绘制的部位配置
      const facialParts = [
        { name: 'left_eye', color: '#00ffaa', animation: false },
        { name: 'right_eye', color: '#00ffaa', animation: false },
        { name: 'left_eyebrow', color: '#00ffaa', animation: false },
        { name: 'right_eyebrow', color: '#00ffaa', animation: false },
        { name: 'nose_bridge', color: '#00ffaa', animation: false },
        { name: 'nose_tip', color: '#00ffaa', animation: false },
        { name: 'outer_lips', color: '#00ffaa', animation: false },
        { name: 'inner_lips', color: '#00ffaa', animation: false },
        { name: 'jaw', color: '#00ffaa', animation: false }
      ];

      // 批量绘制所有部位
      facialParts.forEach(part => {
        drawFacialPart(part.name, part.color, part.animation);
      });

      // 绘制到canvas
      ctx.draw();


    }, 100);
  });
}

const handlePreview = () => {
  // 防止iOS端快速点击导致的状态混乱
  if (isAnimating.value) return

  // 检查用户是否已登录
  const userInfo = uni.getStorageSync('userInfo')

  if (!userInfo) {
    // 未登录，通知父组件显示登录弹窗
    emit('show-login')
    return
  }

  // 已登录，执行原有逻辑
  expandPreview()
}

const expandPreview = () => {
  // if (beforeimgRef.value) {
  //   beforeImgHeight.value = beforeimgRef.value.height
  // }

  // 添加动画状态控制
  isAnimating.value = true
  isExpanded.value = !isExpanded.value

  // 动画完成后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}
let face_aes = ref({})
let detect = ref({})
let generate = ref({})
async function getStatus(operationId) {
  let { data } = await callAiPolling({ operationId, type: 'esthetics' })

  // 计算进度：aiStatus为'1'的数量 / 总数量 * 100
  const completedCount = data.data.filter(item => item.aiStatus == '1').length
  const totalCount = data.data.length
  const percent = Math.round((completedCount / totalCount) * 100)

  // 更新进度
  emit('update:percent', percent)

  // let status = data.data.every(item => item.aiStatus == '1')
  let generateStatus = data.data.filter(item => item.aiType == 'generate')[0].aiStatus
  let faceAesStatus = data.data.filter(item => item.aiType == 'face_aes')[0].aiStatus
  if (faceAesStatus == '1') {
    face_aes.value = JSON.parse(data.data.filter(item => item.aiType == 'face_aes')[0].aiResult)

  } else if (faceAesStatus == '2') {
    uni.showModal({
      title: '提示',
      content: '生成图片失败',
      confirmText:"重新上传",
      success: function (res) {
        if (res.confirm) {
          uni.navigateBack();
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
  let status = data.data.every(item => item.aiStatus == '1')
  if (status) {

    clearInterval(timer)
  }
  if (generateStatus == '1') {
    generate.value = data.data.filter(item => item.aiType == 'generate')[0].aiResult
    // 完成后隐藏loading
    // emit('update:loading', false)
    // emit('update:percent', 100)

    // 10秒后停止加载动画
    stopAllLoadingAnimations()
  } else if (generateStatus == '2') {
    uni.showModal({
      title: '提示',
      content: '生成图片失败',
      confirmText:"重新上传",
      success: function (res) {
        if (res.confirm) {
          uni.navigateBack();
        } else if (res.cancel) {
          console.log('用户点击取消');
        }
      }
    });

    // uni.showToast({
    //   title: '生成图片失败',
    //   duration: 2000,
    //   icon:"none"
    // });
    // clearInterval(timer)
    // stopAllLoadingAnimations()
  }
}

function organizeData(data) {

  face_aes.value = JSON.parse(data.filter(item => item.aiType == 'face_aes')[0].aiResult)
  detect.value = JSON.parse(data.filter(item => item.aiType == 'detect')[0].aiResult)
  generate.value = data.filter(item => item.aiType == 'generate')[0].aiResult

}
const popupVisible = ref(false)
const selectIcon2 = (item) => {
  // activeReport.value = item;
  emit('update:activeReport', item)

  // 重置展开状态，确保每次切换tab时都是收起状态
  isExpanded.value = false
  isAnimating.value = false

  // iOS端需要更长的延迟来确保DOM更新完成
  setTimeout(() => {
    popupVisible.value = true;
  }, 100);
}

const gotoOrgList = () => {
  uni.navigateTo({
    url: '/pages/orgList/index'
  })
}


// 推荐项目数据
const recommendedItems = ref([
  {
    imgSrc: '/static/imgs/ject1.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "明星医师方案",
    category: "玻尿酸+肉毒素+光子嫩肤",
  },
  {
    imgSrc: '/static/imgs/ject2.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "黄金标准方案",
    category: "玻尿酸+肉毒素",
  },
  {
    imgSrc: '/static/imgs/ject3.jpg',
    alt: "Side profile of a young woman, neutral expression, white background",
    title: "经典臻选方案",
    category: "玻尿酸",
  },
]);



watch(() => props.activeReport, () => {
  // 监听activeReport变化，重置展开状态
  isExpanded.value = false
  isAnimating.value = false
}, { deep: true });

// 暴露方法给父组件调用
defineExpose({
  expandPreview
})
</script>

<style lang="scss" scoped>
/* 标签样式 */
.tag {
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

/* 弹窗样式 */
.popup-overlay {

  width: 100%;
  height: 100%;
  // background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.popup-container {
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 4;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  /* iOS兼容性：使用fallback背景色 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top-left-radius: 5rpx;
  border-top-right-radius: 5rpx;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 110rpx;
  margin-bottom: calc(110rpx + constant(safe-area-inset-bottom));
  /* 兼容 IOS<11.2 */
  margin-bottom: calc(110rpx + env(safe-area-inset-bottom));
  /* 兼容 IOS>11.2 */
  padding-bottom: env(safe-area-inset-bottom);
  /* iOS安全区域适配 */
  overflow: hidden;
  will-change: transform, height;
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* iOS点击优化 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  &>view {
    height: 100%;
    display: flex;
    flex-direction: column;

    .popup-content {
      flex: 1;
      overflow-y: auto;
      /* iOS滚动优化 */
      -webkit-overflow-scrolling: touch;
    }
  }
}

.popup-container.collapsed {
  height: 200rpx;
  overflow: hidden;
}

.collapsed {
  .popup-content {
    overflow: hidden !important;
  }
}

.popup-container.expanded {
  max-height: 60vh;
  height: auto;
  overflow-y: auto;
  min-height: 0;
}

.popup-container.slide-up {
  transform: translateY(0) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(0) translateZ(0);
}

.popup-container.slide-down {
  transform: translateY(100%) translateZ(0);
  /* iOS优化：强制硬件加速 */
  -webkit-transform: translateY(100%) translateZ(0);
}

.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
}

.popup-content {
  width: 100%;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  transition: none;
  /* 移除内容区域的过渡动画 */
  /* iOS滚动优化 */
  -webkit-overflow-scrolling: touch;
}

.popup-container.expanded .popup-content {
  overflow-y: auto;
}

.popup-container.collapsed .popup-content {
  overflow-y: auto;
}

/* 横向滚动样式 */
scroll-view ::v-deep {
  .flex {
    flex-wrap: nowrap;
  }
}

/* 底部标签栏样式 */
.bottom-tab {
  position: relative;
}

.bottom-tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 4rpx;
  background-color: #ff4d4f;
}

.inactive-tab {
  color: #999;
}

/* 新增iOS适配样式 */
.ios-safe-area {
  padding-top: env(safe-area-inset-top);
}

.ios-bottom-safe {
  padding-bottom: env(safe-area-inset-bottom);
}

/* iOS滚动优化 */
scroll-view {
  -webkit-overflow-scrolling: touch;
}

/* iOS按钮点击效果优化 */
button {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* iOS特定按钮样式 */
.ios-button {
  /* 防止iOS双击缩放 */
  touch-action: manipulation;
  /* 强制硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  /* 防止点击延迟 */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  /* 防止长按选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* 防止拖拽 */
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
}

/* iOS图片优化 */
img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* Canvas和光点动画样式 */
.image-container {
  position: relative;
  display: inline-block;
}

.light-point {
  position: absolute;
  width: 12px;
  height: 12px;
  background: #00ffaa;
  border-radius: 50%;
  box-shadow: 0 0 15px #00ffaa;
  transform: translate(-50%, -50%);
  z-index: 3;
  transition: all 0.05s linear;
}
</style>